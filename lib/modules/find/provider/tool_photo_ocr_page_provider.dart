// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/utils/permission_dialog_util.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:camera/camera.dart';

class ToolPhotoOcrPageProvider extends ChangeNotifier {
  RefreshController refreshController = RefreshController();

  List<CameraDescription> cameras = [];

  /// 定义一个 cameraController
  late CameraController? cameraController;
  late CameraController? cameraControllerTest;

  /// 需要回传的参数
  String cbParam = '';

  /// 拍照或者从相册选择的临时图片文件
  String tempPictureFile = '';

  /// 是否正在ocr
  bool ocring = false;

  /// 识别结果
  String ocrResult = '';

  /// 初始化控制器的 Future
  late Future<void> initializeControllerFuture;

  final ImagePicker picker = ImagePicker();

  ToolPhotoOcrPageProvider({this.cbParam = ''});

  // initState() async {
  //   cameras = await availableCameras();
  //   cameraController = CameraController(cameras[0], ResolutionPreset.high);
  //   initializeControllerFuture =
  //       cameraController!.initialize(); //初始化是异步的所以用一个Future来赋值
  //   notifyListeners();
  // }

  initCamera() async {
    try {
      cameras = await availableCameras();
    } on CameraException catch (e) {
      print('获取相机设备失败：$e');
    }

    cameraControllerTest =
        CameraController(cameras[0], ResolutionPreset.medium);

    try {
      await cameraControllerTest!.initialize();
    } on CameraException catch (e) {
      print('初始化相机失败：$e');
    }
  }

  // @override
  dispose() {
    // print("销毁==");
    cameraControllerTest!.dispose();
    // super.dispose();
  }

  /// 请求相机权限
  Future<void> askCameraPermission(BuildContext context) async {
    // 首先检查当前权限状态
    final status = await Permission.camera.status;

    // 如果权限已经授予，直接返回
    if (status.isGranted) {
      return;
    }

    // 如果权限被永久拒绝，提示用户去设置页面开启
    if (status.isPermanentlyDenied) {
      if (context.mounted) {
        _showPermissionDeniedDialog(context);
      }
      return;
    }

    // 显示权限申请对话框
    final agree = await PermissionDialogUtil.show(context, [
      PermissionData(
          type: PermissionType.camera,
          description: "用于写作批改或者物品拍照后进行OCR识别或者图片分析。"),
    ]);

    if (!context.mounted) return;

    if (agree != true) {
      // 用户拒绝了权限申请，返回上一页
      Navigator.of(context).pop();
      return;
    }

    // 用户同意后，等待一小段时间确保自定义对话框完全关闭，然后请求系统权限
    await Future.delayed(const Duration(milliseconds: 300));

    if (!context.mounted) return;

    // 请求权限
    var requestStatus = await Permission.camera.request();
    print('相机权限请求结果: $requestStatus');

    if (!requestStatus.isGranted) {
      if (requestStatus.isPermanentlyDenied) {
        // 权限被永久拒绝，提示用户去设置页面开启
        if (context.mounted) {
          _showPermissionDeniedDialog(context);
        }
      } else {
        // 权限被临时拒绝，返回上一页
        EasyLoading.showToast('需要相机权限才能使用此功能');
        if (context.mounted) {
          Navigator.of(context).pop();
        }
      }
    }
  }

  /// 显示权限被拒绝的对话框
  void _showPermissionDeniedDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('权限被拒绝'),
          content: const Text('相机权限被拒绝，请在设置中手动开启相机权限后重试。'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                if (context.mounted) {
                  Navigator.of(context).pop(); // 返回上一页
                }
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(dialogContext).pop();
                // 打开应用设置页面
                await openAppSettings();
                if (context.mounted) {
                  Navigator.of(context).pop(); // 返回上一页
                }
              },
              child: const Text('去设置'),
            ),
          ],
        );
      },
    );
  }

  // 捕获图片
  Future<void> captureImage() async {
    if (cameraControllerTest == null ||
        !cameraControllerTest!.value.isInitialized) {
      return;
    }
    if (cameraControllerTest!.value.isTakingPicture) {
      return;
    }
    try {
      // await _askCameraPermission();
      var result = await cameraControllerTest!.takePicture();
      tempPictureFile = result!.path;
      notifyListeners();
    } catch (e) {
      print(e);
    }

    // XFile? pickedFile =
    //     await picker.pickImage(source: ImageSource.camera, imageQuality: 50);

    // if (pickedFile != null) {
    //   tempPictureFile = pickedFile!.path;
    //   notifyListeners();
    // }
  }

  // 从相册选择图片
  Future<void> pickImage(BuildContext context) async {
    // 首先检查相册权限
    final hasPermission = await PermissionDialogUtil.checkAndRequestPhotoPermission(context);
    if (!hasPermission) {
      return;
    }

    // final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    if (image == null) {
      return;
    }
    tempPictureFile = image.path;
    notifyListeners();
  }

  /// 重置图片
  resetImage() {
    tempPictureFile = '';
    notifyListeners();
  }

  ocr(BuildContext context) async {
    if (ocring || tempPictureFile.isEmpty) {
      return;
    }
    EasyLoading.showToast('正在识别', duration: const Duration(seconds: 15));

    ocring = true;

    // 上传图片获取网络地址
    var uploadResult = await Api.uploadFile(tempPictureFile,
        topicCode: '', scene: 'writing_correction');

    var result =
        await Api.ocr("${uploadResult.data!.fileUrl!}?imageMogr2/format/jpeg");
    // var result = await Api.ocr("https://cos.xinquai.com/writing_correction/000/20241221/718/user/7181734774572.8960633image_picker_A1BD455B-F6FD-4677-9CAA-28892EE05FF9-28789-0000047BF88218AB.jpg?imageMogr2/format/jpeg");
    ocrResult = result.data ?? '';

    ocring = false;
    EasyLoading.dismiss();

    // 返回上一页面, 带上识别结果和回调参数
    if (context.mounted) {
      Navigator.pop(context, {'result': ocrResult, 'cbParam': cbParam});
    }
  }
}
